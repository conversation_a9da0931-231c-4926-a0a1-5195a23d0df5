["test_app.py::TestEndpointIntegration::test_all_endpoints_accessible", "test_app.py::TestEndpointIntegration::test_invalid_endpoints_return_404", "test_app.py::TestHealthEndpoint::test_health_check_content_type", "test_app.py::TestHealthEndpoint::test_health_check_endpoint_success", "test_app.py::TestHealthEndpoint::test_health_check_model_not_loaded", "test_app.py::TestHomeEndpoint::test_home_endpoint_content_type", "test_app.py::TestHomeEndpoint::test_home_endpoint_returns_welcome_message", "test_app.py::TestPredictEndpoint::test_predict_endpoint_age_boundaries", "test_app.py::TestPredictEndpoint::test_predict_endpoint_bmi_boundaries", "test_app.py::TestPredictEndpoint::test_predict_endpoint_city_normalization", "test_app.py::TestPredictEndpoint::test_predict_endpoint_different_occupations", "test_app.py::TestPredictEndpoint::test_predict_endpoint_empty_city", "test_app.py::TestPredictEndpoint::test_predict_endpoint_empty_request_body", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_age_negative", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_age_too_high", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_height_negative", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_height_too_high", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_income_negative", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_occupation", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_smoker_type", "test_app.py::TestPredictEndpoint::test_predict_endpoint_invalid_weight_negative", "test_app.py::TestPredictEndpoint::test_predict_endpoint_malformed_json", "test_app.py::TestPredictEndpoint::test_predict_endpoint_missing_required_field", "test_app.py::TestPredictEndpoint::test_predict_endpoint_model_exception", "test_app.py::TestPredictEndpoint::test_predict_endpoint_smoker_true", "test_app.py::TestPredictEndpoint::test_predict_endpoint_tier_1_city", "test_app.py::TestPredictEndpoint::test_predict_endpoint_tier_2_city", "test_app.py::TestPredictEndpoint::test_predict_endpoint_tier_3_city", "test_app.py::TestPredictEndpoint::test_predict_endpoint_valid_input_success", "test_app.py::TestPredictEndpoint::test_predict_endpoint_wrong_data_types"]