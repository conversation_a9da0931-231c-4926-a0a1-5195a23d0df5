import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json

from app import app
from schema.user_input import UserInput
from schema.prediction_response import PredictionResponse


# Create TestClient instance
client = TestClient(app)


class TestHomeEndpoint:
    """Test cases for the home endpoint (GET /)"""
    
    def test_home_endpoint_returns_welcome_message(self):
        """Test that home endpoint returns correct welcome message"""
        response = client.get("/")
        
        assert response.status_code == 200
        assert response.json() == {"message": "Insurance Premium Predictor API"}
    
    def test_home_endpoint_content_type(self):
        """Test that home endpoint returns JSON content type"""
        response = client.get("/")
        
        assert response.headers["content-type"] == "application/json"


class TestHealthEndpoint:
    """Test cases for the health check endpoint (GET /health)"""
    
    def test_health_check_endpoint_success(self):
        """Test that health check endpoint returns correct status"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify required fields are present
        assert "status" in data
        assert "version" in data
        assert "model_loaded" in data
        
        # Verify field values
        assert data["status"] == "ok"
        assert data["version"] == "1.0.0"
        assert isinstance(data["model_loaded"], bool)
    
    def test_health_check_content_type(self):
        """Test that health check endpoint returns JSON content type"""
        response = client.get("/health")
        
        assert response.headers["content-type"] == "application/json"
    
    @patch('app.model', None)
    def test_health_check_model_not_loaded(self):
        """Test health check when model is not loaded"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["model_loaded"] is False


class TestPredictEndpoint:
    """Test cases for the prediction endpoint (POST /predict)"""
    
    def get_valid_input_data(self):
        """Helper method to get valid input data for testing"""
        return {
            "age": 30,
            "weight": 70.5,
            "height": 1.75,
            "income_lpa": 8.5,
            "smoker": False,
            "city": "Mumbai",
            "occupation": "private_job"
        }
    
    def test_predict_endpoint_valid_input_success(self):
        """Test prediction endpoint with valid input data"""
        valid_data = self.get_valid_input_data()
        
        response = client.post("/predict", json=valid_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "response" in data
        response_data = data["response"]
        
        assert "predicted_category" in response_data
        assert "confidence" in response_data
        assert "class_probabilities" in response_data
        
        # Verify data types
        assert isinstance(response_data["predicted_category"], str)
        assert isinstance(response_data["confidence"], float)
        assert isinstance(response_data["class_probabilities"], dict)
        
        # Verify confidence is between 0 and 1
        assert 0 <= response_data["confidence"] <= 1
        
        # Verify class probabilities sum to approximately 1
        prob_sum = sum(response_data["class_probabilities"].values())
        assert abs(prob_sum - 1.0) < 0.01
    
    def test_predict_endpoint_tier_1_city(self):
        """Test prediction with tier 1 city"""
        data = self.get_valid_input_data()
        data["city"] = "Delhi"  # Tier 1 city
        
        response = client.post("/predict", json=data)
        assert response.status_code == 200
    
    def test_predict_endpoint_tier_2_city(self):
        """Test prediction with tier 2 city"""
        data = self.get_valid_input_data()
        data["city"] = "Jaipur"  # Tier 2 city
        
        response = client.post("/predict", json=data)
        assert response.status_code == 200
    
    def test_predict_endpoint_tier_3_city(self):
        """Test prediction with tier 3 city (not in tier 1 or 2)"""
        data = self.get_valid_input_data()
        data["city"] = "Unknown City"  # Tier 3 city
        
        response = client.post("/predict", json=data)
        assert response.status_code == 200
    
    def test_predict_endpoint_different_occupations(self):
        """Test prediction with different valid occupations"""
        valid_occupations = [
            'retired', 'freelancer', 'student', 'government_job',
            'business_owner', 'unemployed', 'private_job'
        ]
        
        for occupation in valid_occupations:
            data = self.get_valid_input_data()
            data["occupation"] = occupation
            
            response = client.post("/predict", json=data)
            assert response.status_code == 200, f"Failed for occupation: {occupation}"
    
    def test_predict_endpoint_smoker_true(self):
        """Test prediction with smoker=True"""
        data = self.get_valid_input_data()
        data["smoker"] = True
        
        response = client.post("/predict", json=data)
        assert response.status_code == 200
    
    def test_predict_endpoint_age_boundaries(self):
        """Test prediction with different age groups"""
        age_test_cases = [
            20,   # young (< 25)
            30,   # adult (25-44)
            50,   # middle_aged (45-59)
            65    # senior (>= 60)
        ]
        
        for age in age_test_cases:
            data = self.get_valid_input_data()
            data["age"] = age
            
            response = client.post("/predict", json=data)
            assert response.status_code == 200, f"Failed for age: {age}"
    
    def test_predict_endpoint_bmi_boundaries(self):
        """Test prediction with different BMI values affecting lifestyle risk"""
        bmi_test_cases = [
            {"weight": 60, "height": 1.75},   # Normal BMI (~19.6)
            {"weight": 85, "height": 1.75},   # High BMI (~27.8)
            {"weight": 95, "height": 1.75}    # Very high BMI (~31.0)
        ]
        
        for bmi_case in bmi_test_cases:
            data = self.get_valid_input_data()
            data.update(bmi_case)
            
            response = client.post("/predict", json=data)
            assert response.status_code == 200
    
    # Invalid input test cases
    def test_predict_endpoint_missing_required_field(self):
        """Test prediction endpoint with missing required field"""
        data = self.get_valid_input_data()
        del data["age"]  # Remove required field
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422  # Validation error
    
    def test_predict_endpoint_invalid_age_negative(self):
        """Test prediction endpoint with negative age"""
        data = self.get_valid_input_data()
        data["age"] = -5
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_age_too_high(self):
        """Test prediction endpoint with age too high"""
        data = self.get_valid_input_data()
        data["age"] = 150
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_weight_negative(self):
        """Test prediction endpoint with negative weight"""
        data = self.get_valid_input_data()
        data["weight"] = -10.5
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_height_negative(self):
        """Test prediction endpoint with negative height"""
        data = self.get_valid_input_data()
        data["height"] = -1.75
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_height_too_high(self):
        """Test prediction endpoint with height too high"""
        data = self.get_valid_input_data()
        data["height"] = 3.0
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_income_negative(self):
        """Test prediction endpoint with negative income"""
        data = self.get_valid_input_data()
        data["income_lpa"] = -5.0
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_occupation(self):
        """Test prediction endpoint with invalid occupation"""
        data = self.get_valid_input_data()
        data["occupation"] = "invalid_occupation"
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_invalid_smoker_type(self):
        """Test prediction endpoint with invalid smoker field type"""
        data = self.get_valid_input_data()
        data["smoker"] = "yes"  # Should be boolean
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_empty_city(self):
        """Test prediction endpoint with empty city"""
        data = self.get_valid_input_data()
        data["city"] = ""
        
        response = client.post("/predict", json=data)
        assert response.status_code == 422
    
    def test_predict_endpoint_city_normalization(self):
        """Test that city names are properly normalized"""
        data = self.get_valid_input_data()
        data["city"] = "  mumbai  "  # Should be normalized to "Mumbai"
        
        response = client.post("/predict", json=data)
        assert response.status_code == 200
    
    def test_predict_endpoint_wrong_data_types(self):
        """Test prediction endpoint with wrong data types"""
        invalid_data_cases = [
            {"age": "thirty"},  # String instead of int
            {"weight": "70.5"},  # String instead of float
            {"height": "1.75"},  # String instead of float
            {"income_lpa": "8.5"},  # String instead of float
        ]
        
        for invalid_case in invalid_data_cases:
            data = self.get_valid_input_data()
            data.update(invalid_case)
            
            response = client.post("/predict", json=data)
            assert response.status_code == 422
    
    def test_predict_endpoint_empty_request_body(self):
        """Test prediction endpoint with empty request body"""
        response = client.post("/predict", json={})
        assert response.status_code == 422
    
    def test_predict_endpoint_malformed_json(self):
        """Test prediction endpoint with malformed JSON"""
        response = client.post(
            "/predict",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
    
    @patch('app.predict_output')
    def test_predict_endpoint_model_exception(self, mock_predict):
        """Test prediction endpoint when model raises an exception"""
        mock_predict.side_effect = Exception("Model prediction failed")
        
        data = self.get_valid_input_data()
        response = client.post("/predict", json=data)
        
        assert response.status_code == 500
        assert "Model prediction failed" in response.json()


class TestEndpointIntegration:
    """Integration tests for multiple endpoints"""
    
    def test_all_endpoints_accessible(self):
        """Test that all endpoints are accessible"""
        # Test GET endpoints
        home_response = client.get("/")
        health_response = client.get("/health")
        
        assert home_response.status_code == 200
        assert health_response.status_code == 200
        
        # Test POST endpoint
        valid_data = {
            "age": 30,
            "weight": 70.5,
            "height": 1.75,
            "income_lpa": 8.5,
            "smoker": False,
            "city": "Mumbai",
            "occupation": "private_job"
        }
        predict_response = client.post("/predict", json=valid_data)
        assert predict_response.status_code == 200
    
    def test_invalid_endpoints_return_404(self):
        """Test that invalid endpoints return 404"""
        invalid_endpoints = ["/invalid", "/predict/invalid", "/health/status"]
        
        for endpoint in invalid_endpoints:
            response = client.get(endpoint)
            assert response.status_code == 404


if __name__ == "__main__":
    pytest.main([__file__])
